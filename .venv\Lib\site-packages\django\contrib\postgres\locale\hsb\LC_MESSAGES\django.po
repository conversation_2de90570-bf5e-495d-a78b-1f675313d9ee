# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2016-2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-11 20:56+0200\n"
"PO-Revision-Date: 2020-05-12 20:01+0000\n"
"Last-Translator: Transifex Bot <>\n"
"Language-Team: Upper Sorbian (http://www.transifex.com/django/django/"
"language/hsb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: hsb\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n"
"%100==4 ? 2 : 3);\n"

msgid "PostgreSQL extensions"
msgstr "Rozšěrjenja PostgreSQL"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "Zapisk %(nth)s w pólnym wariabli njeje so wokrućił:"

msgid "Nested arrays must have the same length."
msgstr "Zakašćikowane pólne wariable maja samsnu dołhosć."

msgid "Map of strings to strings/nulls"
msgstr "Konwertowanje znamješkowych rjećazkow do znamješkowych rjećazkow/nulow"

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr "Hódnota \" %(key)s\" znamješkowy rjećazk abo null njeje."

msgid "Could not load JSON data."
msgstr "JSON-daty njedachu so začitać."

msgid "Input must be a JSON dictionary."
msgstr "Zapodaće dyrbi JSON-słownik być."

msgid "Enter two valid values."
msgstr "Zapodajće dwě płaćiwej hódnoće."

msgid "The start of the range must not exceed the end of the range."
msgstr "Spočatk wobłuka njesmě kónc wobłuka překročić."

msgid "Enter two whole numbers."
msgstr "Zapodajće dwě cyłej ličbje."

msgid "Enter two numbers."
msgstr "Zapodajće dwě ličbje."

msgid "Enter two valid date/times."
msgstr "Zapódajće dwě płaćiwej datowej/časowej podaći."

msgid "Enter two valid dates."
msgstr "Zapodajće dwě płaćiwej datowej podaći."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
"Lisćina %(show_value)d element wobsahuje, wona njeměła wjace hač "
"%(limit_value)d wobsahować."
msgstr[1] ""
"Lisćina %(show_value)d elementaj wobsahuje, wona njeměła wjace hač "
"%(limit_value)d wobsahować."
msgstr[2] ""
"Lisćina %(show_value)d elementy wobsahuje, wona njeměła wjace hač "
"%(limit_value)d wobsahować."
msgstr[3] ""
"Lisćina %(show_value)d elementow wobsahuje, wona njeměła wjace hač "
"%(limit_value)d wobsahować."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
"Lisćina %(show_value)d element wobsahuje, wona njeměła mjenje hač "
"%(limit_value)d wobsahować."
msgstr[1] ""
"Lisćina %(show_value)d elementaj wobsahuje, wona njeměła mjenje hač "
"%(limit_value)d wobsahować."
msgstr[2] ""
"Lisćina %(show_value)d elementy wobsahuje, wona njeměła mjenje hač "
"%(limit_value)d wobsahować."
msgstr[3] ""
"Lisćina %(show_value)d elementow wobsahuje, wona njeměła mjenje hač "
"%(limit_value)d wobsahować."

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "Někotre kluče faluje: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr "Někotre njeznate kluče su so podali: %(keys)s"

#, python-format
msgid ""
"Ensure that this range is completely less than or equal to %(limit_value)s."
msgstr "Zawěsćće. zo tutón wobłuk je mjeńši hač abo runja %(limit_value)s."

#, python-format
msgid ""
"Ensure that this range is completely greater than or equal to "
"%(limit_value)s."
msgstr "Zawěsćće, zo tutón wobłuk je wjetši hač abo runja %(limit_value)s."
