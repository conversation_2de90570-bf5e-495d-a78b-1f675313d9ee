# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2021-04-11 13:15+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: English (Australia) (http://www.transifex.com/django/django/"
"language/en_AU/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: en_AU\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Sites"
msgstr "Sites"

msgid "The domain name cannot contain any spaces or tabs."
msgstr "The domain name cannot contain any spaces or tabs."

msgid "domain name"
msgstr "domain name"

msgid "display name"
msgstr "display name"

msgid "site"
msgstr "site"

msgid "sites"
msgstr "sites"
