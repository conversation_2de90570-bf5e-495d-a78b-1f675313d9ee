# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <o.ch<PERSON><PERSON><PERSON>@gmail.com>, 2014
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <oleksand<PERSON>.<EMAIL>>, 2013
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-23 18:54+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Ukrainian (http://www.transifex.com/django/django/language/"
"uk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: uk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n % 10 == 1 && n % 100 != "
"11 ? 0 : n % 1 == 0 && n % 10 >= 2 && n % 10 <= 4 && (n % 100 < 12 || n % "
"100 > 14) ? 1 : n % 1 == 0 && (n % 10 ==0 || (n % 10 >=5 && n % 10 <=9) || "
"(n % 100 >=11 && n % 100 <=14 )) ? 2: 3);\n"

msgid "Sites"
msgstr "Сайти"

msgid "The domain name cannot contain any spaces or tabs."
msgstr "Доменне ім'я не може містити пробіли або символи табуляції."

msgid "domain name"
msgstr "доменне ім'я"

msgid "display name"
msgstr "відображуване ім'я"

msgid "site"
msgstr "сайт"

msgid "sites"
msgstr "сайти"
