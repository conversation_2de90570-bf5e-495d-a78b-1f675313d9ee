import os
import socket

APPLICATION_NAME = "transport_company"
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
BASE_DIR = os.path.dirname(PROJECT_DIR)

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = '&b*81-_j886mxzgd6cx&(q)gyx%g)o&46u8o1b3@r&xh(r3=td'
# GM sm4 密钥
GM_SM4_KEY = "8BFFFBA3F4FE441521B5A62D2F70C36D"
# 定时任务开关
SCHEDULER_SWITCH = False

EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_USE_TLS = False
EMAIL_USE_SSL = True
EMAIL_HOST = "smtp.exmail.qq.com"
EMAIL_PORT = 465
EMAIL_HOST_USER = "<EMAIL>"
EMAIL_HOST_PASSWORD = "6LEaNwKagmbGofXs"

FILE_TYPE = [
    'wgt', 'apk', 'zip', 'py', 'png', 'jpg', 'jpeg',
    'bmp', 'gif', 'doc', 'txt', 'docx', 'pdf', 'ppt',
    'csv', 'exe', 'apk', 'excel', 'word', 'xls', 'xlsx',
    'mp4', 'm2v', 'mkv', 'mp3', 'wav', 'wmv', 'm4a'
]

ROLE_GROUP = {1: '车辆管理', 2: '合同管理', 3: '账号管理'}
CONTRACT_TYPE = {'1': '收运合同', '2': '运输合同'}
STREET_TYPE = {'1': '街道', '2': '末端厂'}
POWWER_TYPE = {'1': '燃油', '2': '混动', '3': '新能源'}
CAR_EMISSION_GRADE = {'1': '国1', '2': '国2', '3': '国3', '4': '国4', '5': '国5', '6': '国6'}
OIL_TYPE = {'1': '汽油', '2': '柴油'}
FACILITY_STATUS = {'1': '审核中', '2': '审核通过'}
STATUS_TYPE = {'1': '新增申请', '2': '变更申请', '3': '注销申请'}
STATUS_NAME = {0: '草稿', 1: '待处理', 2: '未通过', 3: '已通过'}
CAR_TYPE = {'01738800ca3511eaafc6485f99c1b734': '餐厨垃圾车', 'a80ae89cc0de11eaa8a9000c29d3cc31': '厨余垃圾车',
            'a826fa5ac0de11eaa8a9000c29d3cc31': '其他垃圾车', 'a838398cc0de11eaa8a9000c29d3cc31': '转运车',
            'a84386fcc0de11eaa8a9000c29d3cc31': '粪便运输车', 'a84c546cc0de11eaa8a9000c29d3cc31': '水罐车',
            'bebab964ca3411eab56c485f99c1b734': '单臂吊车'}

ZHONG_PLANT = ['董村垃圾综合处理厂', '通州区再生能源发电厂', '通州区有机质生态资源处理站']
COMPANY_TYPE = {'401': '事业单位', '402': '国有企业', '403': '集体企业', '404': '民营企业'}
TRANSPORT_TYPE = {'1': '直收直运', '8': '直运清运', '2': '转运'}
RUBBISH_TYPE = {'25a7185abf5611eaa8a9000c29d3cc31': '家庭厨余垃圾', 'b84b760ec02a11eaa8a9000c29d3cc31': '餐厨垃圾',
                'b8c900bac02a11eaa8a9000c29d3cc31': '其他垃圾'}

POWER_TYPE = {'1': '燃油', '2': '混动', '3': '新能源', '4': '烧气'}
POWER_TYPE_NAME = {v: k for k, v in POWER_TYPE.items()}

CAR_CODE = {'1+25a7185abf5611eaa8a9000c29d3cc31+1': '200', '1+25a7185abf5611eaa8a9000c29d3cc31+3': '201',
            '1+25a7185abf5611eaa8a9000c29d3cc31+2': '202',
            '1+25a7185abf5611eaa8a9000c29d3cc31+4': '203',
            '1+b84b760ec02a11eaa8a9000c29d3cc31+1': '210', '1+b84b760ec02a11eaa8a9000c29d3cc31+3': '211',
            '1+b84b760ec02a11eaa8a9000c29d3cc31+2': '212',
            '1+b84b760ec02a11eaa8a9000c29d3cc31+4': '213',
            '1+b8c900bac02a11eaa8a9000c29d3cc31+1': '220', '1+b8c900bac02a11eaa8a9000c29d3cc31+3': '221',
            '1+b8c900bac02a11eaa8a9000c29d3cc31+2': '222',
            '1+b8c900bac02a11eaa8a9000c29d3cc31+4': '223',
            '8+25a7185abf5611eaa8a9000c29d3cc31+1': '230', '8+25a7185abf5611eaa8a9000c29d3cc31+3': '231',
            '8+25a7185abf5611eaa8a9000c29d3cc31+2': '232',
            '8+25a7185abf5611eaa8a9000c29d3cc31+4': '233',
            '8+b84b760ec02a11eaa8a9000c29d3cc31+1': '240', '8+b84b760ec02a11eaa8a9000c29d3cc31+3': '241',
            '8+b84b760ec02a11eaa8a9000c29d3cc31+2': '242',
            '8+b84b760ec02a11eaa8a9000c29d3cc31+4': '243',
            '8+b8c900bac02a11eaa8a9000c29d3cc31+1': '250', '8+b8c900bac02a11eaa8a9000c29d3cc31+3': '251',
            '8+b8c900bac02a11eaa8a9000c29d3cc31+2': '252',
            '8+b8c900bac02a11eaa8a9000c29d3cc31+4': '253',
            '2+25a7185abf5611eaa8a9000c29d3cc31+1': '260', '2+25a7185abf5611eaa8a9000c29d3cc31+3': '261',
            '2+25a7185abf5611eaa8a9000c29d3cc31+2': '262',
            '2+25a7185abf5611eaa8a9000c29d3cc31+4': '263',
            '2+b8c900bac02a11eaa8a9000c29d3cc31+1': '270', '2+b8c900bac02a11eaa8a9000c29d3cc31+3': '271',
            '2+b8c900bac02a11eaa8a9000c29d3cc31+2': '272',
            '2+b8c900bac02a11eaa8a9000c29d3cc31+4': '273',
            }

CAR_UPDATE = {'car_num': '车牌', 'service_area': '服务区域', 'street_name': '街道名称', 'carry_weight': '车辆载重量(t)',
              'transport_type': '车辆属性', 'power_type': '能源类型', 'rubbish_type': '垃圾类型',
              'car_type_id': '车辆类型', 'is_tu': '是否按规范喷涂', 'is_monitor': '是否加装卫星定位设备',
              'is_weight': '车辆是否加装称重计量系统', 'is_identification': '是否加装身份识别系统',
              'factory_name': '单位(设施)名称',
              'is_quality': '是否加装质量研判监控系统', 'is_access': '车辆是否接入区级生活垃圾全流程精细化管理服务平台',
              'dest_cleaning_point_id': "单位(垃圾楼)名称"}
CONTRACT_NEW_UPDATE = {'contract_num': '合同编号', 'rubbish_type': '垃圾品类', 'collect_date': '收集时间',
                       'sign_date': '合同签订日期', 'contacts': '清运公司联系人', 'phone': '清运公司联系电话',
                       'viald_time': '服务期限', 'contract_picture': '合同上传', 'rubbish_type_name': '垃圾类型名称'}

OTHER_CAR_UPDATE = {'car_num': '车牌', 'district': '所属区', 'source_area': '垃圾来源区',
                    'source_street': '垃圾来源街道',
                    'transport_type': '清运方式', 'rubbish_type': '垃圾类型', 'company': '车辆权属单位名称',
                    'source_cleaning_point': '来源密闭式清洁站或中转点', 'rubbish_name': '垃圾品类',
                    'receiving_unit': '接收单位（末端厂）', 'is_rubbish': '是否生活垃圾车',
                    'company_type': '权属单位类型',
                    'uhf_device': 'RFID电子标签识别设备', 'is_weight': '车载计量称重设备', 'is_gps': 'GPS定位系统',
                    'rfid_label': 'RFID电子标签', 'city_system': '接入市级系统', 'upload_type': '数据上传方式',
                    'dock_company': '对接公司', 'remark': '备注'}
hostname = socket.gethostname()

# 23服务器
if hostname in ['Server-61198fa2-11ab-4fd6-8c45-cd138a1534fe.novalocal']:
    SETTING_BASE_IP = 'http://***********:8290'
    MANAGE_AUTH_IP = 'http://***********:8296'
    CONTRACT_IP = 'http://***********:8522'
    # CITY_IP = 'http://***********:8094'
    CITY_IP = 'http://dev-jfpt.ztbory.com'
    TRANSPORT_IP = 'http://***********:8522'

    CACHES = {
        'default': {
            'BACKEND': 'django_redis.cache.RedisCache',
            'LOCATION': 'redis://************:6379/14',  # test
            "OPTIONS": {
                "CLIENT_CLASS": "django_redis.client.DefaultClient",

                "PASSWORD": "",
            },
        },
    }

# 20服务器
elif hostname in ['Server-e6cede4f-d941-4d4c-becf-e2399a27b12e.novalocal',
                  'core-business-0001', 'core-business-0002',
                  'core-business-0003']:
    SETTING_BASE_IP = 'http://***********:8290'
    MANAGE_AUTH_IP = 'http://***********:8296'
    CONTRACT_IP = 'http://************:8522'
    CITY_IP = 'http://***********:8094'
    TRANSPORT_IP = 'http://************:8522'

    CACHES = {
        'default': {
            'BACKEND': 'django_redis.cache.RedisCache',
            'LOCATION': 'redis://************:6379/14',  # test
            "OPTIONS": {
                "CLIENT_CLASS": "django_redis.client.DefaultClient",

                "PASSWORD": "",
            },
        },
    }

else:
    # SETTING_BASE_IP = 'http://ljflbasedata.ztbory.com'
    SETTING_BASE_IP = 'http://***********:8080'
    # SETTING_BASE_IP = 'http://************:8777'
    MANAGE_AUTH_IP = 'http://dev-auth.ztbory.com'
    CITY_IP = 'http://***********:8188'
    CONTRACT_IP = 'http://***********:8005'
    # CONTRACT_IP = 'http://************:8522'
    TRANSPORT_IP = 'http://***********:8765'

    CACHES = {
        'default': {
            'BACKEND': 'django_redis.cache.RedisCache',
            'LOCATION': 'redis://***************:6379/14',
            "OPTIONS": {
                "CLIENT_CLASS": "django_redis.client.DefaultClient",

                "PASSWORD": "",
            },
        },
    }

CONTRACT_UPDATE = {'name': '合同名称', 'con_type': '合同类型', 'jia': '甲方', 'yi': '乙方', 'qua_type': '所需资质',
                   'content': '备注',
                   'contract_picture': '合同照片', 'qualification_picture': '资质照片', 'signed_time': '签订时间',
                   'viald_time': '合同有效期', 'jia_type': '甲方类型'}

ALLOWED_HOSTS = ['*']
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django_filters',
    'rest_framework',
    'rest_framework.authtoken',  # 设置token
    'corsheaders',  # 解决跨域
    'transport',
    'user',
    'contract'
]
AUTH_USER_MODEL = "user.User"
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    # 'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    "Lic.core.ApiMiddleWare",
    "utils.middleware.ResponseFilterMiddleware",  # 添加响应过滤中间件
]

ROOT_URLCONF = 'project.urls'

# 设置token 过期时间为12小时
REST_FRAMEWORK_TOKEN_EXPIRE_MINUTES = 60 * 12

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'project.wsgi.application'

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

# Internationalization
# https://docs.djangoproject.com/en/1.11/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

TIME_ZONE = 'Asia/Shanghai'

USE_I18N = True

USE_L10N = True

USE_TZ = False

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/1.11/howto/static-files/

STATIC_URL = '/static/'
STATIC_DIR = os.path.join(BASE_DIR, "static")
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, "static"),
]

REST_FRAMEWORK = {

    # 认证方式
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'Lic.core.ExpiringTokenAuthentication',
        # 'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.TokenAuthentication',
        # 'rest_framework_jwt.authentication.JSONWebTokenAuthentication',
        'rest_framework.authentication.BasicAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ),
    # "EXCEPTION_HANDLER": "ztbory_django_shield.restframework.shield_error_handler",

    # 权限
    'DEFAULT_PERMISSION_CLASSES': [
        'utils.permission.CheckCodingPermission',
        # 'utils.permission.IsInternalNetworkOrAuthenticated',
        # 'utils.permission.MyPermission',
        # 'utils.permission.MyPermission',
        # 'utils.permission.MyPermissionCity',
        # 'utils.permission.MyPermissionArea',
        # 'utils.permission.MyPermissionStreet',
        # 'rest_framework.permissions.IsAuthenticated'
    ],

    'EXCEPTION_HANDLER': 'common.tools.handle_exception.custom_exception_handler',
    'DEFAULT_PAGINATION_CLASS': 'common.tools.handle_pagination.MyPageNumberPagination',
    # render的配置 响应时自动调用render方法,将字典转化为json数据
    'DEFAULT_RENDERER_CLASSES': (
        'Lic.core.CustomRenderer',
    ),
    # 过滤后端
    'DEFAULT_FILTER_BACKENDS': (
        'django_filters.rest_framework.DjangoFilterBackend',
    ),
    'DATETIME_FORMAT': "%Y-%m-%d %H:%M:%S",
}

# CACHES = {
#     'default': {
#         'BACKEND': 'django_redis.cache.RedisCache',
#         'LOCATION': 'redis://127.0.0.1:6379/15',
#         "OPTIONS": {
#             "CLIENT_CLASS": "django_redis.client.DefaultClient",
#
#             "PASSWORD": "",
#         },
#     },
# }

"""忽略跨域请求"""
CORS_ALLOW_CREDENTIALS = True
CORS_ORIGIN_ALLOW_ALL = True
CORS_ORIGIN_WHITELIST = ()
CORS_ALLOW_METHODS = (
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
    'VIEW',
)
CORS_ALLOW_HEADERS = (
    'XMLHttpRequest',
    'X_FILENAME',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
)

# 日志配置
BASE_LOG_DIR = os.path.join(BASE_DIR, "log")
# 日志文件不存在，建立文件
if not os.path.exists(BASE_LOG_DIR):
    os.makedirs(BASE_LOG_DIR)

AUTH_APPID = 117998218
AUTH_APP_SECRETE = "jEOWjo3igrmoiXiq5SKBFs0G9dm42Tw7"

LOGGING = {
    'version': 1,  # 保留字
    'disable_existing_loggers': False,  # 禁用已经存在的logger实例
    # 日志文件的格式
    'formatters': {
        # 详细的日志格式
        'standard': {
            'format': '[%(asctime)s][%(threadName)s:%(thread)d][task_id:%(name)s][%(filename)s:%(lineno)d]'
                      '[%(levelname)s][%(message)s]'
        },
        # 简单的日志格式
        'simple': {
            'format': '[%(levelname)s][%(asctime)s][%(filename)s:%(lineno)d]%(message)s'
        },
        # 定义一个特殊的日志格式
        'collect': {
            'format': '[%(levelname)s][%(asctime)s][%(filename)s:%(lineno)d]%(message)s'
        }
    },
    # 过滤器
    'filters': {
        'require_debug_true': {
            '()': 'django.utils.log.RequireDebugTrue',
        },
    },
    # 处理器
    'handlers': {
        # 在终端打印
        'console': {
            'level': 'DEBUG',
            'filters': ['require_debug_true'],  # 只有在Django debug为True时才在屏幕打印日志
            'class': 'logging.StreamHandler',  #
            'formatter': 'simple'
        },
        # 默认的
        'default': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',  # 保存到文件，自动切
            'filename': os.path.join(BASE_LOG_DIR, "transport_info.log"),  # 日志文件
            'maxBytes': 1024 * 1024 * 50,  # 日志大小 50M
            'backupCount': 3,  # 最多备份几个
            'formatter': 'standard',
            'encoding': 'utf-8',
        },
        # 专门用来记错误日志
        'error': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',  # 保存到文件，自动切
            'filename': os.path.join(BASE_LOG_DIR, "transport_err.log"),  # 日志文件
            'maxBytes': 1024 * 1024 * 50,  # 日志大小 50M
            'backupCount': 5,
            'formatter': 'standard',
            'encoding': 'utf-8',
        },
        # 专门定义一个收集特定信息的日志
        'collect': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',  # 保存到文件，自动切
            'filename': os.path.join(BASE_LOG_DIR, "transport_collect.log"),
            'maxBytes': 1024 * 1024 * 50,  # 日志大小 50M
            'backupCount': 5,
            'formatter': 'collect',
            'encoding': "utf-8"
        }
    },
    'loggers': {
        # 默认的logger应用如下配置
        '': {
            'handlers': ['default', 'console', 'error'],  # 上线之后可以把'console'移除
            'level': 'DEBUG',
            'propagate': True,  # 向不向更高级别的logger传递
        },
        # 名为 'collect'的logger还单独处理
        'collect': {
            'handlers': ['console', 'collect'],
            'level': 'INFO',
        }
    },
}
DEFAULT_AUTO_FIELD = 'django.db.models.AutoField'

# 多数据库配置
DATABASE_ROUTERS = ['project.database_router.BaseDatabaseV3Router']
DATABASE_APPS_MAPPING = {
    'facility': 'default',
    'ljfl_db': 'base',
    'ljfl_declare_db': 'ljfl_declare_db',
}

SHIELD = {
    "cryptor": {
        "private_key": "C1D69117B6E93AE194516BF1CFC33C9E7B2A379FF9AC47684E2C87AA66163EF3",
        "public_key": (
            "04F0D1AF00D6E0DDBBEE344A99A5924ABFD1125B704A2499735D40DF5019A2B33BEF4F111A3"
            "EA9383637AA2A75B92ACA4B92460C9DA7CE4C897FFEAC35412D467F"
        ),
    },
    "slide_captcha": {
        "gallery": "LjflGallery"
    }
}
