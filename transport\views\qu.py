import datetime
import logging
import time

from django.db import transaction
from django.db.models import Count
from django.template.loader import render_to_string
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.views import APIView

from common.exceptions import CheckException
from common.tools.car_add import add_car_db, get_ip
from transport.views.models_db import CarDeviceUploadStatus, VirtualAreaStreet, CarBase, CarBaseOperate
from utils.cryptor import sm4_cryptor
from utils.permission import MyPermissionArea, MyPermissionCity, MyPermission
from common.tools.common import get_uuid, get_date, group_by, \
    add_company_city, add_car_city, add_relation_driver_user, relation_driver_logoff, \
    add_company_area_city, car_opera_detail, check_car_opera_detail, other_car_opera_detail, \
    check_other_car_opera_detail, edit_company_street
from common.tools.handle_pagination import MyPageNumberPagination
from common.tools.const import NonresidentRubbishes, __area_coding_to_name__, RoleEnum
from transport.filters import CompanyAreaFilter, QuCompanyFilter, QuCarFilter, \
    CheckCompanyFilter, CheckCarFilter, CheckQualificateFilter, \
    QuQualificateFilter, CheckWorkerFilter, QuWorkerFilter, AreaOperateLogFilter, CityCompanyFilter, \
    QuContractNewFilter
from transport.models import Company, Contract, Car, CompanyArea, Qualificate, Worker, AreaOperateLog, SendEmail, \
    ContractNew, CarOperate, CompanyOperate, CompanyOperateDetail, OtherCarOperate, OtherCar, TransportDriver
from transport.serializer import CheckCompanyAreaSer, CarSer, QualificateSer, WorkerSer, \
    AreaOperateLogSer,  CityCompanySer, EnterWorkerSer, ContractNewSer, CheckCompanySer
from user.models import User
from utils.scripts import random_password
from django.conf import settings
logger = logging.getLogger("loggers")


class CheckCompany(APIView):
    """区审核公司通过不通过"""
    permission_classes = [MyPermissionArea]

    def post(self, request, **kwargs):
        company_id = self.request.POST.get('company_id', None)
        area_coding = self.request.POST.get('area_coding', None)
        rubbishes = request.data.get('rubbishes', NonresidentRubbishes.RESTAURANTS)
        manager = request.data.get('managers')
        instance = CompanyArea.objects.filter(
            company_id=company_id, area_coding=area_coding, rubbishes=rubbishes
        ).first()
        if instance.last_street_coding:
            return self.post_modify(request, **kwargs)
        else:
            return self.post_first_times(request, **kwargs)

    # 首次审核
    def post_first_times(self, request, **kwargs):
        logger.info('开始===================')
        company_id = self.request.POST.get('company_id', None)
        area_coding = self.request.POST.get('area_coding', None)
        status = self.request.POST.get('status', None)
        no_reject = self.request.POST.get('no_reject', None)
        enter_time = datetime.datetime.now()
        username = request.user['manager']['username']
        rubbishes = request.data.get('rubbishes', NonresidentRubbishes.RESTAURANTS)
        if status and int(status) == 0:  # 通过
            with transaction.atomic():
                logger.info('开始事务===================')
                sid = transaction.savepoint()
                Company.objects.filter(company_id=company_id).update(status=1)
                area_queryset = CompanyArea.objects.filter(company_id=company_id, area_coding=area_coding,
                                                           rubbishes=rubbishes)
                area_queryset.update(enter_time=enter_time, enter_type=1, enter_operater=username, status=3)
                logger.info('公司审核状态编辑===================')

                company_obj = Company.objects.filter(company_id=company_id)
                company = company_obj.values('name', 'email', 'add_status')
                com_area = area_queryset.values('street_name', 'street_coding', 'transport_company_area_id')
                if com_area and com_area[0]:
                    street_name = com_area[0].get('street_name')
                    street_coding = com_area[0].get('street_coding')
                    transport_company_area_id = com_area[0].get('transport_company_area_id')
                else:
                    street_name = ''
                    street_coding = ''
                    transport_company_area_id = ''

                if company and company[0]:
                    email = company[0].get('email')
                    c_name = company[0].get('name')
                    add_status = company[0].get('add_status')
                else:
                    email = ''
                    c_name = ''
                    add_status = 0
                AreaOperateLog.objects.create(area_coding=area_coding, username=username,
                                              operate_data='{}:区审核公司通过'.format(c_name))
                logger.info('AreaOperateLog===================')
                c_response = add_company_area_city(area_coding, company_id, street_name, street_coding,
                                                   transport_company_area_id, rubbishes)
                if c_response.get('code') != 201:
                    return Response(c_response)
                if add_status == 0:
                    # 审核通过后, 用户改为可以登录状态
                    user = User.objects.filter(company_uid=company_id)
                    user.update(user_status=1)
                    # 审核通过后, 市级添加公司信息
                    c_response = add_company_city(area_coding, c_name, company_id, street_name, company_obj.first())
                    if c_response.get('code') != 201:
                        return Response(c_response)
                    logger.info('审核通过后, 市级添加公司信息户')
                    logger.info('send_email')
                    u_name = user.filter(user_status=1)
                    account = u_name[0] if u_name and u_name[0] else ''
                    # 写邮件html
                    template_name = "email/management_registration_approved.html"
                    context = {
                        "management_name": account.nickname if account else "",
                        "management_host": settings.HOST_WEBSERVICE,
                        "username": account.username if account else "",
                        "password": sm4_cryptor.decrypt(
                            account.password_crypt) if account and account.password_crypt else "",
                    }
                    html_message = render_to_string(template_name, context)

                    SendEmail.objects.create(account=account, rec_email=email, send_name=username, rec_name=c_name,
                                             type=1, html_message=html_message)

                    # send_email(1, account=u_name, rec=email, send=username, rec_name=c_name)
                    logger.info('send_email_end--------------------')
                    transaction.savepoint_commit(sid)
                    Company.objects.filter(company_id=company_id).update(add_status=1)
                    logger.info('end===============')
                operate_id = CompanyOperateDetail.objects.filter(update_data='服务区域',
                                                                 old_content=__area_coding_to_name__.get(area_coding)). \
                    values_list('operate_id', flat=True)
                operate_id = operate_id[0] if operate_id and operate_id[0] else ''
                CompanyOperate.objects.filter(company_id=transport_company_area_id, is_delete=2, operate_id=operate_id). \
                    update(is_delete=0, create_time=datetime.datetime.now(), status=3)
                CompanyOperateDetail.objects.filter(update_data='街道名称', operate_id=operate_id). \
                    update(old_content=street_name)
                return Response({'msg': "区审核公司通过成功", 'code': 200})
        else:
            if not no_reject:
                return Response({'msg': "请输入审核不通过原因", 'code': 400})
            # Company.objects.filter(company_id=company_id).update(status=2)
            CompanyArea.objects.filter(company_id=company_id, area_coding=area_coding, rubbishes=rubbishes) \
                .update(enter_time=enter_time, no_reject=no_reject, enter_operater=username, status=2)
            company = Company.objects.filter(company_id=company_id).values('email', 'name')
            if company and company[0]:
                email = company[0].get('email')
                c_name = company[0].get('name')
            else:
                email = ''
                c_name = ''

            # 写邮件html
            template_name = "email/management_registration_rejected.html"
            context = {
                "management_name": c_name,
                "reject_reason": no_reject,
                "send_name": username,
            }
            html_message = render_to_string(template_name, context)


            SendEmail.objects.create(rec_email=email, send_name=username, rec_name=c_name,
                                     type=0, no_reject=no_reject, html_message=html_message)

            # send_email(0, no_reject=no_reject, rec=email, send=username, rec_name=c_name)
            AreaOperateLog.objects.create(area_coding=area_coding, username=username,
                                          operate_data='{}:区审核公司不通过'.format(c_name))
            operate_id = CompanyOperateDetail.objects.filter(update_data='服务区域',
                                                             old_content=__area_coding_to_name__.get(area_coding)). \
                values_list('operate_id', flat=True)
            operate_id = operate_id[0] if operate_id and operate_id[0] else ''
            CompanyOperate.objects.filter(company_id=company_id, is_delete=2, operate_id=operate_id). \
                update(is_delete=1)
            return Response({'msg': "区审核公司不通过成功", 'code': 200})

    # 街道变更审核
    def post_modify(self, request, *args, **kwargs):
        username = request.user['manager']['username']
        no_reject = self.request.POST.get('no_reject', None)
        enter_time = datetime.datetime.now()

        company_id = self.request.POST.get('company_id', None)
        area_coding = self.request.POST.get('area_coding', None)
        rubbishes = request.data.get('rubbishes', NonresidentRubbishes.RESTAURANTS)
        status = self.request.POST.get('status', None)
        instance = CompanyArea.objects.filter(
            company_id=company_id, area_coding=area_coding, rubbishes=rubbishes
        ).first()

        company = Company.objects.filter(company_id=company_id).first()
        if status and int(status) == 0:  # 通过
            with transaction.atomic():
                edit_company_street(instance.area_coding, instance.company_id, instance.street_name,
                                    instance.street_coding, rubbishes)
                operate_id = get_uuid()
                user = User.objects.filter(company_uid=instance.company_id).first()
                CompanyOperate.objects.create(operate_id=operate_id, company_id=instance.transport_company_area_id,
                                              username=user.username,
                                              status=instance.status, type_o='修改', is_delete=0)
                CompanyOperateDetail.objects.create(operate_id=operate_id, update_data='街道名称',
                                                    old_content=instance.last_street_name,
                                                    new_content=instance.street_name)

                AreaOperateLog.objects.create(area_coding=area_coding, username=username,
                                              operate_data='{}:区审核公司通过'.format(company.name))
                CompanyArea.objects.filter(company_id=company_id, area_coding=area_coding, rubbishes=rubbishes) \
                    .update(enter_time=enter_time, enter_operater=username, status=3,
                            street_name=instance.street_name, street_coding=instance.street_coding,
                            last_street_name="", last_street_coding="")
            return Response({'msg': "区审核公司通过成功", 'code': 200})
        else:
            if not no_reject:
                return Response({'msg': "请输入审核不通过原因", 'code': 400})
            with transaction.atomic():
                CompanyArea.objects.filter(company_id=company_id, area_coding=area_coding, rubbishes=rubbishes) \
                    .update(enter_time=enter_time, no_reject=no_reject, enter_operater=username, status=3,
                            street_name=instance.last_street_name, street_coding=instance.last_street_coding,
                            last_street_name="", last_street_coding="")
                AreaOperateLog.objects.create(area_coding=area_coding, username=username,
                                              operate_data='{}:区审核公司不通过成功'.format(company.name))
            return Response({'msg': "区审核公司不通过成功", 'code': 200})


class CheckContract(APIView):
    """区审核合同通过不通过"""
    permission_classes = [MyPermissionArea]

    def post(self, request, **kwargs):
        contract_id = self.request.POST.get('contract_id', None)
        status = self.request.POST.get('status', None)
        no_reject = self.request.POST.get('no_reject', None)
        status_type = self.request.POST.get('status_type', None)
        enter_time = datetime.datetime.now()
        username = request.user['manager']['username']
        contract = Contract.objects.filter(contract_id=contract_id).values('name', 'service_area', 'company_id')
        if contract and contract[0]:
            name = contract[0].get('name')
            service_area = contract[0].get('service_area')
            company_id = contract[0].get('company_id')
        else:
            name = ''
            service_area = ''
            company_id = ''
        if status and int(status) == 0:  # 通过
            Contract.objects.filter(contract_id=contract_id).update(status=3, enter_time=enter_time, enter_type=1,
                                                                    enter_operater=username, selected_type=1)
            # 注销审核通过后，删除该条记录
            if status_type and int(status_type) == 3:
                Contract.objects.filter(contract_id=contract_id).update(is_delete=1)
            AreaOperateLog.objects.create(area_coding=service_area, username=username,
                                          operate_data='{}:区审核合同通过'.format(name))

            # 审核通过，查看公司是否为入选公司，不是入选公司修改入选状态和入选时间
            if not CompanyArea.objects.filter(company_id=company_id, selected_type=1, area_coding=service_area,
                                              is_delete=0).exists():
                CompanyArea.objects.filter(company_id=company_id, area_coding=service_area).update(
                    selected_type=1, selected_time=enter_time)
                Car.objects.filter(company_id=company_id, service_area=service_area, enter_type=1). \
                    update(selected_type=1)
                Qualificate.objects.filter(company_id=company_id, service_area=service_area, enter_type=1). \
                    update(selected_type=1)
                Worker.objects.filter(company_id=company_id, service_area=service_area, enter_type=1). \
                    update(selected_type=1)
            # 审核通过后, 市级添加合同信息
            # if service_area != '110112000000':
            #     con_obj = Contract.objects.filter(contract_id=contract_id).first()
            #     c_response = add_contract_city(con_obj)
            #     if c_response.get('code') != 0:
            #         return Response(c_response)
            return Response({'msg': "区审核合同通过成功", 'code': 200})
        else:
            if not no_reject:
                return Response({'msg': "请输入审核不通过原因", 'code': 400})
            Contract.objects.filter(contract_id=contract_id).update(status=2, no_reject=no_reject,
                                                                    enter_time=enter_time, enter_operater=username)
            AreaOperateLog.objects.create(area_coding=service_area, username=username,
                                          operate_data='{}:区审核合同不通过'.format(name))
            return Response({'msg': "区审核合同不通过成功", 'code': 200})


def update_transport_driver(name=None, password=None, company_id=None, relation_id=None):
    try:
        # # 保存密码用于下载查看
        relation_id = relation_id or get_uuid()

        base_car_obj = CarBase.objects.filter(car_num=name).order_by("-id").first()
        insert_data = dict(
            role=RoleEnum.TransportDriver,
            # email="",
            transport_company_id=company_id,
            relation_id=relation_id,
            nickname=name,
            uuid_id=base_car_obj.car_id if base_car_obj else None,
        )
        if password:
            insert_data["password"] = sm4_cryptor.encrypt(password)
        TransportDriver.objects.update_or_create(username=name, defaults=insert_data)
    except Exception as e:
        logger.error(f"保存车辆司机账号失败:{e}")


class CheckCar(APIView):
    """区审核车辆通过不通过"""
    permission_classes = [MyPermissionArea]

    def post(self, request, **kwargs):
        car_id = self.request.POST.get('car_id', None)
        status = self.request.POST.get('status', None)
        no_reject = self.request.POST.get('no_reject', None)
        status_type = self.request.POST.get('status_type', None)
        username = request.user['manager']['username']
        enter_time = datetime.datetime.now()
        now_time = int(time.time())
        queryset = Car.objects.filter(car_id=car_id)
        car_data = queryset.values(
            'car_num', 'service_area', 'company_id', 'relation_id', 'status',
            'check_type', 'street_name', 'rubbish_type', 'dest_cleaning_point_id', 'transport_type'
        ).first()
        if car_data:
            name = car_data.get('car_num')
            service_area = car_data.get('service_area')
            company_id = car_data.get('company_id')
            relation_id = car_data.get('relation_id')
            car_status = car_data.get('status')
            check_type = car_data.get('check_type')
            street_name = car_data.get('street_name')
            rubbish_type = car_data.get('rubbish_type')
            transport_type = car_data.get('transport_type')
        else:
            name = ''
            service_area = ''
            company_id = ''
            relation_id = ''
            car_status = ''
            street_name = ''
            rubbish_type = ''
            check_type = 0
            transport_type = 0
        if status and int(status) == 0:  # 通过
            car_queryset_change_data = {
                'status': 3,
                'enter_time': enter_time,
                'enter_type': 1,
                'enter_operater': username,
                'selected_type': 1
            }
            if status_type and int(status_type) == 3:
                # 注销车辆
                car_queryset_change_data['is_delete'] = 1
                queryset.update(**car_queryset_change_data)
                # 注销审核通过后，从市级删除车辆信息
                CarBase.objects.filter(car_num=name).update(standing_book=2)
                # 同步市级
                area_name = __area_coding_to_name__.get(service_area, '')
                org_data = dict(area_name=area_name, street_name=street_name, type_id=rubbish_type)
                CarBaseOperate.objects.create(car_num=name, type_o=1, create_time=now_time, update_time=now_time,
                                              operate_id=get_uuid(), org_data=org_data)

                # c_response = del_car_city(car_id)
                # if c_response.get('code') != 0 and c_response.get('code') != 2004:
                #     return Response(c_response)
                if car_status and int(car_status) == 1:
                    token = self.request.META.get('HTTP_AUTHORIZATION')
                    user_response = relation_driver_logoff(token, 1, relation_id)
                    if user_response.get('code') != 200:
                        return Response(user_response)
            else:
                # # 新增、修改通过
                # if check_type == 0:
                #     try:
                #         device_status = self.check_car_device_status(name, transport_type=transport_type)
                #         car_queryset_change_data.update(device_status)
                #     except CheckException as e:
                #         return Response({'msg': str(e), 'code': 400})
                
                # 如果公司入选，车辆也改为入选
                if CompanyArea.objects.filter(company_id=company_id, selected_type=1,
                                              area_coding=service_area, is_delete=0).exists():
                    car_queryset_change_data['selected_type'] = 1
                # 新增审核通过，创建车辆司机账号
                password = None
                if relation_id:
                    token = self.request.META.get('HTTP_AUTHORIZATION')
                    user_response = relation_driver_logoff(token, 0, relation_id)
                    if user_response.get('code') != 200:
                        return Response(user_response)
                else:
                    password = random_password(length=10)
                    relation_id = get_uuid()
                    if name:
                        token = self.request.META.get('HTTP_AUTHORIZATION')
                        response = add_relation_driver_user(name, relation_id, token, password)

                        if response.get('code') != 200:
                            if isinstance(response, dict):
                                msg = response.get('msg')
                                msg = msg.replace('用户名', '车辆')
                                response['msg'] = msg
                            return Response(response)
                        else:
                            car_queryset_change_data['relation_id'] = relation_id

                # 审核通过后, 从市级添加车辆信息
                car_obj = Car.objects.filter(car_id=car_id).first()
                logger.info('审核通过后, 从市级添加车辆信息')
                c_response = add_car_city(car_obj)
                logger.info('==============:%s' % c_response.get('code'))
                if c_response.get('code') != 0 and c_response.get('code') != 201:
                    return Response(c_response)

                queryset.update(**car_queryset_change_data)
                update_transport_driver(name=name, password=password, company_id=company_id, relation_id=relation_id)

                # 车辆添加记录
                if check_type == 0:
                    car_opera_detail(queryset.values())
                else:
                    # 同步市级
                    check_car_opera_detail(queryset.values(), name)
            AreaOperateLog.objects.create(area_coding=service_area, username=username,
                                          operate_data='{}:区审核车辆通过'.format(name))

            return Response({'msg': "区审核车辆通过成功", 'code': 200})
        else:
            if not no_reject:
                return Response({'msg': "请输入审核不通过原因", 'code': 400})
            Car.objects.filter(car_id=car_id).update(status=2, no_reject=no_reject, enter_time=enter_time,
                                                     enter_operater=username)
            AreaOperateLog.objects.create(area_coding=service_area, username=username,
                                          operate_data='{}:区审核车辆不通过'.format(name))
            CarOperate.objects.filter(is_delete=2).update(is_delete=1)
            return Response({'msg': "区审核车辆不通过成功", 'code': 200})
    
    
    def check_car_device_status(self, car_num, transport_type):
        """新增审核的时候检查车辆设备状态
        直收直运车（transport_type=1）三种状态都要有
        直运清运（transport_type=8）只要求有gps定位
        """
        error_msg = """
        根据北京市车辆管理条例的要求，设备校验未完成的车辆无法通过审核。具体要求如下：
            1.直收直运车辆，必须安装卫星定位系统、称重计量装置以及身份识别设备。
            2.直运清运车辆则至少需要配备卫星定位设备。
        确保所有相关设备安装并校验完毕，是通过审核的前提条件。请尽快安排相关设备的安装与校验工作，以便顺利完成审核流程。
        """
        car_device_status = CarDeviceUploadStatus.objects.filter(car_num=car_num, is_deleted=0).values_list(
            'satellite_status',
            'weight_status',
            'identity_status'
        ).first()
        if not car_device_status:
            raise CheckException(error_msg)
        
        if transport_type == '1':
            if not all(car_device_status):
                raise CheckException(error_msg)
        elif transport_type == '8':
            if not car_device_status[0]:
                raise CheckException(error_msg)
        else:
            if not car_device_status[0]:
                raise CheckException(error_msg)
        return {
            'satellite_status': car_device_status[0],
            'weight_status': car_device_status[1],
            'identity_status': car_device_status[2],
        }


class CheckQualificate(APIView):
    """区审核资质通过不通过"""
    permission_classes = [MyPermissionArea]

    def post(self, request, **kwargs):
        qualificate_id = self.request.POST.get('qualificate_id', None)
        status = self.request.POST.get('status', None)
        no_reject = self.request.POST.get('no_reject', None)
        status_type = self.request.POST.get('status_type', None)
        enter_time = datetime.datetime.now()
        username = request.user['manager']['username']
        queryset = Qualificate.objects.filter(qualificate_id=qualificate_id)
        qual_data = queryset.values('name', 'service_area', 'company_id')
        if qual_data and qual_data[0]:
            name = qual_data[0].get('name')
            service_area = qual_data[0].get('service_area')
            company_id = qual_data[0].get('company_id')
        else:
            name = ''
            service_area = ''
            company_id = ''
        if status and int(status) == 0:  # 通过
            queryset.update(status=3, enter_time=enter_time, enter_type=1, enter_operater=username)
            # 注销审核通过后，删除该条记录
            if status_type and int(status_type) == 3:
                queryset.update(is_delete=1)
            else:
                # 如果公司入选，资质也改为入选
                if CompanyArea.objects.filter(company_id=company_id, selected_type=1,
                                              area_coding=service_area, is_delete=0).exists():
                    queryset.update(selected_type=1)
            AreaOperateLog.objects.create(area_coding=service_area, username=username,
                                          operate_data='{}:区审核资质通过'.format(name))
            return Response({'msg': "区审核资质通过成功", 'code': 200})
        else:
            if not no_reject:
                return Response({'msg': "请输入审核不通过原因", 'code': 400})
            Qualificate.objects.filter(qualificate_id=qualificate_id).update(status=2, no_reject=no_reject,
                                                                             enter_time=enter_time,
                                                                             enter_operater=username)
            AreaOperateLog.objects.create(area_coding=service_area, username=username,
                                          operate_data='{}:区审核资质通过'.format(name))
            return Response({'msg': "区审核资质不通过成功", 'code': 200})


class CheckWorker(APIView):
    """区审核人员通过不通过"""
    permission_classes = [MyPermissionArea]

    def post(self, request, **kwargs):
        worker_id = self.request.POST.get('worker_id', None)
        status = self.request.POST.get('status', None)
        no_reject = self.request.POST.get('no_reject', None)
        status_type = self.request.POST.get('status_type', None)
        enter_time = datetime.datetime.now()
        username = request.user['manager']['username']
        queryset = Worker.objects.filter(worker_id=worker_id)
        worker_data = queryset.values('name', 'service_area', 'company_id')
        if worker_data and worker_data[0]:
            name = worker_data[0].get('name')
            service_area = worker_data[0].get('service_area')
            company_id = worker_data[0].get('company_id')
        else:
            name = ''
            service_area = ''
            company_id = ''
        if status and int(status) == 0:  # 通过
            queryset.update(status=3, enter_time=enter_time, enter_type=1, enter_operater=username)
            # 注销审核通过后，删除该条记录
            if status_type and int(status_type) == 3:
                queryset.update(is_delete=1)
            else:
                # 如果公司入选，人员也改为入选
                if CompanyArea.objects.filter(company_id=company_id, selected_type=1,
                                              area_coding=service_area, is_delete=0).exists():
                    queryset.update(selected_type=1)
            AreaOperateLog.objects.create(area_coding=service_area, username=username,
                                          operate_data='{}:区审核人员通过'.format(name))
            return Response({'msg': "区审核人员通过成功", 'code': 200})
        else:
            if not no_reject:
                return Response({'msg': "请输入审核不通过原因", 'code': 400})
            Worker.objects.filter(worker_id=worker_id).update(status=2, no_reject=no_reject, enter_time=enter_time,
                                                              enter_operater=username)
            AreaOperateLog.objects.create(area_coding=service_area, username=username,
                                          operate_data='{}:区审核人员不通过'.format(name))
            return Response({'msg': "区审核人员不通过成功", 'code': 200})


class QuCompanyVs(viewsets.ModelViewSet):
    """入围入选公司信息"""

    permission_classes = [MyPermission]
    queryset = Company.objects.filter(is_delete=0).order_by('-id')
    serializer_class = CityCompanySer
    pagination_class = MyPageNumberPagination  # 分页
    filter_fields = ['id', 'company_id', 'name']  # 过滤器参数
    search_fields = ('id', 'company_id', 'name')  # 搜索选项
    filter_class = QuCompanyFilter

    def filter_queryset(self, queryset):
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(self.request, queryset, self)
        return queryset

    def get_queryset(self):
        area_coding = self.request.GET.get('area_coding', None)
        street_coding = self.request.GET.get('street_coding', None)
        street_area = self.request.GET.get('street_area', None)
        enter_type = self.request.GET.get('enter_type', None)
        start_enter_time = self.request.GET.get('start_enter_time', None)
        end_enter_time = self.request.GET.get('end_enter_time', None)
        start_selected_time = self.request.GET.get('start_selected_time', None)
        end_selected_time = self.request.GET.get('end_selected_time', None)
        area_query = CompanyArea.objects.filter(status=3)
        com_list = area_query.values_list('company_id', flat=True)
        self.queryset = self.queryset.filter(company_id__in=com_list)
        # if enter_type and int(enter_type) == 1:
        #     area_query = CompanyArea.objects.filter(enter_type=1)
        # else:
        #     area_query = CompanyArea.objects.filter(selected_type=1)
        if area_coding:
            com_list = area_query.filter(area_coding=area_coding).values_list('company_id', flat=True)
            self.queryset = self.queryset.filter(company_id__in=com_list)

        # 街道账号查询
        if street_coding:
            street_coding = str(street_coding)[0:6] + '000000'
            com_list = area_query.filter(area_coding=street_coding).values_list('company_id', flat=True)
            self.queryset = self.queryset.filter(company_id__in=com_list)
        # 公司街道字段
        if street_area:
            com_list = area_query.filter(street_coding__icontains=street_area).values_list('company_id', flat=True)
            self.queryset = self.queryset.filter(company_id__in=com_list)

        if start_enter_time and end_enter_time:
            com__list = area_query.filter(enter_time__gte=start_enter_time, enter_time__lte=end_enter_time). \
                values_list('company_id', flat=True)
            self.queryset = self.queryset.filter(company_id__in=com__list)

        if start_selected_time and end_selected_time:
            com__list = area_query.filter(selected_time__gte=start_selected_time,
                                          selected_time__lte=end_selected_time). \
                values_list('company_id', flat=True)
            self.queryset = self.queryset.filter(company_id__in=com__list)

        return self.queryset


class QuCarVs(viewsets.ModelViewSet):
    """入围入选车辆信息"""

    permission_classes = [MyPermission]
    queryset = Car.objects.filter(is_delete=0, status=3).order_by('-id')
    serializer_class = CarSer
    pagination_class = MyPageNumberPagination  # 分页
    filter_fields = ['id', 'car_num', 'car_type_id']  # 过滤器参数
    search_fields = ('id', 'car_num', 'car_type_id')  # 搜索选项
    filter_class = QuCarFilter


class QuContractNewVs(viewsets.ModelViewSet):
    """入围入选合同信息"""

    permission_classes = [MyPermission]
    queryset = ContractNew.objects.filter(is_delete=0).order_by('-id')
    serializer_class = ContractNewSer
    pagination_class = MyPageNumberPagination  # 分页
    filter_fields = ['id', 'name']  # 过滤器参数
    search_fields = ('id', 'name')  # 搜索选项
    filter_class = QuContractNewFilter


class QuQualificateVs(viewsets.ModelViewSet):
    """入围入选资质信息"""

    permission_classes = [MyPermission]
    queryset = Qualificate.objects.filter(is_delete=0).order_by('-id')
    serializer_class = QualificateSer
    pagination_class = MyPageNumberPagination  # 分页
    filter_fields = ['id', 'name']  # 过滤器参数
    search_fields = ('id', 'name')  # 搜索选项
    filter_class = QuQualificateFilter


class QuWorkerVs(viewsets.ModelViewSet):
    """入围入选人员信息"""

    permission_classes = [MyPermission]
    queryset = Worker.objects.filter(is_delete=0).order_by('-id')
    serializer_class = EnterWorkerSer
    pagination_class = MyPageNumberPagination  # 分页
    filter_fields = ['id', 'name']  # 过滤器参数
    search_fields = ('id', 'name')  # 搜索选项
    filter_class = QuWorkerFilter


# class StreetContractVs(viewsets.ModelViewSet):
#     """街道合同信息"""
#
#     permission_classes = [MyPermissionStreet]
#     queryset = Contract.objects.filter(is_delete=0, enter_type=1).order_by('-id')
#     serializer_class = ContractSer
#     pagination_class = MyPageNumberPagination  # 分页
#     filter_fields = ['id', 'name']  # 过滤器参数
#     search_fields = ('jia', 'yi')  # 搜索选项
#     filter_class = StreetContractFilter


class CheckCompanyVs(viewsets.ModelViewSet):
    """审核公司信息"""

    permission_classes = [MyPermission]
    queryset = Company.objects.filter(is_delete=0).order_by('-id')
    serializer_class = CheckCompanySer
    pagination_class = MyPageNumberPagination  # 分页
    filter_fields = ['id', 'name']  # 过滤器参数
    search_fields = ('id', 'name')  # 搜索选项
    filter_class = CheckCompanyFilter

    def get_queryset(self):
        area_coding = self.request.GET.get('area_coding', None)
        street_area = self.request.GET.get('street_area', None)
        start_enter_time = self.request.GET.get('start_enter_time', None)
        end_enter_time = self.request.GET.get('end_enter_time', None)
        status = self.request.GET.get('status', None)
        virtual_area = self.request.GET.get("virtual_area")

        if area_coding:
            com__list = CompanyArea.objects.filter(area_coding=area_coding, is_delete=0). \
                values_list('company_id', flat=True)
            self.queryset = self.queryset.filter(company_id__in=com__list)
        if street_area:
            com_list = CompanyArea.objects.filter(street_coding__icontains=street_area, is_delete=0). \
                values_list('company_id', flat=True)
            self.queryset = self.queryset.filter(company_id__in=com_list)
        if start_enter_time and end_enter_time:
            com__list = CompanyArea.objects.filter(enter_time__gte=start_enter_time,
                                                   enter_time__lte=end_enter_time). \
                values_list('company_id', flat=True)
            self.queryset = self.queryset.filter(company_id__in=com__list)
        if status:
            area_queryset = CompanyArea.objects.filter(status=status)
            if area_coding:
                area_queryset = area_queryset.filter(area_coding=area_coding)
            com__list = area_queryset.values_list('company_id', flat=True)
            self.queryset = self.queryset.filter(company_id__in=com__list)
        if virtual_area:
            streets = VirtualAreaStreet.objects.filter(
                virtual_area_code=virtual_area
            ).values_list("street_code", flat=True)
            company_ids = []
            for street in streets:
                if area_coding:
                    com__list = CompanyArea.objects.filter(area_coding=area_coding, street_coding__icontains=street,
                                                           is_delete=0, status=status). \
                        values_list('company_id', flat=True)
                    if com__list and com__list[0]:
                        company_ids.append(com__list[0])
            self.queryset = self.queryset.filter(company_id__in=company_ids)

        return self.queryset


class CheckCompanyNewVs(viewsets.ModelViewSet):
    """审核公司信息"""

    permission_classes = [MyPermission]
    queryset = CompanyArea.objects.filter(is_delete=0).order_by('-id')
    serializer_class = CheckCompanyAreaSer
    pagination_class = MyPageNumberPagination  # 分页
    filter_fields = ['id', 'name']  # 过滤器参数
    search_fields = ('id', 'name')  # 搜索选项
    filter_class = CompanyAreaFilter


class CheckCarVs(viewsets.ModelViewSet):
    """审核车辆信息"""

    permission_classes = [MyPermission]
    queryset = Car.objects.filter(is_delete=0).exclude(status=0).order_by('-id')
    serializer_class = CarSer
    pagination_class = MyPageNumberPagination  # 分页
    filter_fields = ['id', 'name']  # 过滤器参数
    search_fields = ('id', 'name')  # 搜索选项
    filter_class = CheckCarFilter


# class CheckContractVs(viewsets.ModelViewSet):
#     """审核合同信息"""
#
#     permission_classes = [MyPermissionArea]
#     queryset = Contract.objects.filter(is_delete=0).exclude(status=0).order_by('-id')
#     serializer_class = ContractSer
#     pagination_class = MyPageNumberPagination  # 分页
#     filter_fields = ['id', 'name']  # 过滤器参数
#     search_fields = ('id', 'name')  # 搜索选项
#     filter_class = CheckContractFilter


class CheckQualificateVs(viewsets.ModelViewSet):
    """审核资质信息"""

    permission_classes = [MyPermissionArea]
    queryset = Qualificate.objects.filter(is_delete=0).exclude(status=0).order_by('-id')
    serializer_class = QualificateSer
    pagination_class = MyPageNumberPagination  # 分页
    filter_fields = ['id', 'name']  # 过滤器参数
    search_fields = ('id', 'name')  # 搜索选项
    filter_class = CheckQualificateFilter


class CheckWorkerVs(viewsets.ModelViewSet):
    """审核人员信息"""

    permission_classes = [MyPermissionArea]
    queryset = Worker.objects.filter(is_delete=0).exclude(status=0).order_by('-id')
    serializer_class = WorkerSer
    pagination_class = MyPageNumberPagination  # 分页
    filter_fields = ['id', 'name']  # 过滤器参数
    search_fields = ('id', 'name')  # 搜索选项
    filter_class = CheckWorkerFilter


class QuOperateLogVs(viewsets.ModelViewSet):
    """区操作日志"""
    permission_classes = [MyPermissionArea]
    queryset = AreaOperateLog.objects.all()
    serializer_class = AreaOperateLogSer
    pagination_class = None  # 分页
    filter_fields = ['id', 'username']  # 过滤器参数
    search_fields = ('id', 'username')  # 搜索选项
    filter_class = AreaOperateLogFilter

    def filter_queryset(self, queryset):
        for backend in list(self.filter_backends):
            queryset = backend().filter_queryset(self.request, queryset, self)
        stime = self.request.GET.get('start_create_time', None)
        etime = self.request.GET.get('start_create_time', None)
        if stime and etime:
            start_create_time, end_create_time = get_date(stime, etime)
            queryset = queryset.filter(create_time__gte=start_create_time, create_time__lte=end_create_time)
        return queryset


class QuLogUser(APIView):
    """区日志操作人列表"""
    permission_classes = [MyPermissionArea]

    def get(self, request, **kwargs):
        area_coding = request.GET.get('coding', '')
        user_log = AreaOperateLog.objects.filter(area_coding=area_coding).values_list('username', flat=True)
        user_list = []
        if user_log:
            user_list = list(set(user_log))

        return Response({'data': user_list, 'code': 200})


class CityCompanyVs(viewsets.ModelViewSet):
    """市公司详情"""

    permission_classes = [MyPermissionCity]
    queryset = Company.objects.filter(is_delete=0).order_by('-id')
    serializer_class = CityCompanySer
    pagination_class = MyPageNumberPagination  # 分页
    filter_fields = ['id', 'name']  # 过滤器参数
    search_fields = ('id', 'name')  # 搜索选项
    filter_class = CityCompanyFilter


class CityStatistics(APIView):
    """市入围详情统计"""
    permission_classes = [MyPermissionCity]

    def get(self, request, **kwargs):
        area_coding = self.request.GET.get('area_coding', None)
        enter_type = self.request.GET.get('enter_type', 1)
        if area_coding:
            if int(enter_type) == 1:
                com_query = CompanyArea.objects.filter(area_coding=area_coding, enter_type=1, is_delete=0)
                car_query = Car.objects.filter(service_area=area_coding, enter_type=1, is_delete=0)
                cont_query = Contract.objects.filter(service_area=area_coding, enter_type=1, is_delete=0)
                qua_query = Qualificate.objects.filter(service_area=area_coding, enter_type=1, is_delete=0)
                worker_query = Worker.objects.filter(service_area=area_coding, enter_type=1, is_delete=0)
            else:
                com_query = CompanyArea.objects.filter(area_coding=area_coding, selected_type=1, is_delete=0)
                car_query = Car.objects.filter(service_area=area_coding, selected_type=1, is_delete=0)
                cont_query = Contract.objects.filter(service_area=area_coding, selected_type=1, is_delete=0)
                qua_query = Qualificate.objects.filter(service_area=area_coding, selected_type=1, is_delete=0)
                worker_query = Worker.objects.filter(service_area=area_coding, selected_type=1, is_delete=0)
        else:
            if int(enter_type) == 1:
                com_query = CompanyArea.objects.filter(enter_type=1, is_delete=0)
                car_query = Car.objects.filter(enter_type=1, is_delete=0)
                cont_query = Contract.objects.filter(enter_type=1, is_delete=0)
                qua_query = Qualificate.objects.filter(enter_type=1, is_delete=0)
                worker_query = Worker.objects.filter(enter_type=1, is_delete=0)
            else:
                com_query = CompanyArea.objects.filter(selected_type=1, is_delete=0)
                car_query = Car.objects.filter(selected_type=1, is_delete=0)
                cont_query = Contract.objects.filter(selected_type=1, is_delete=0)
                qua_query = Qualificate.objects.filter(selected_type=1, is_delete=0)
                worker_query = Worker.objects.filter(selected_type=1, is_delete=0)
        company_data = com_query.values('area_coding', 'company_id')
        res_list = []
        if company_data:
            group_company_data = group_by(list(company_data), 'area_coding')

            res = {}
            company_count_dict = {}
            for area_coding, company_list in group_company_data:
                if area_coding:
                    if area_coding not in company_count_dict.keys():
                        company_count_dict[area_coding] = 0
                    for company in company_list:
                        if company.get('area_coding') == area_coding:
                            company_count_dict[area_coding] += 1
                    area_name = __area_coding_to_name__.get(area_coding, '')
                    res[area_name] = {'company': company_count_dict[area_coding], 'car': 0, 'contract': 0,
                                      'qualificate': 0, 'worker': 0,
                                      'area_coding': area_coding}
            car_data = car_query.values('service_area', 'car_id')
            group_car_data_data = group_by(list(car_data), 'service_area')
            car_count_dict = {}
            for area_coding, car_list in group_car_data_data:
                if area_coding:
                    if area_coding not in car_count_dict.keys():
                        car_count_dict[area_coding] = 0
                    for car in car_list:
                        car_count_dict[area_coding] += 1
                    area_name = __area_coding_to_name__.get(area_coding, '')
                    if area_name:
                        if res.get(area_name, None):
                            res[area_name]['car'] = car_count_dict[area_coding]

            # contract_data = cont_query.values('service_area', 'contract_id')
            # group_contract_data_data = group_by(list(contract_data), 'service_area')
            # contract_count_dict = {}
            # for area_coding, contract_list in group_contract_data_data:
            #     if area_coding:
            #         if area_coding not in contract_count_dict.keys():
            #             contract_count_dict[area_coding] = 0
            #         for contract in contract_list:
            #             contract_count_dict[area_coding] += 1
            #         area_name = __area_coding_to_name__.get(area_coding, '')
            #         if area_name:
            #             if res.get(area_name, None):
            #                 res[area_name]['contract'] = contract_count_dict[area_coding]

            qualificate_data = qua_query.values('service_area', 'qualificate_id')
            group_qualificate_data_data = group_by(list(qualificate_data), 'service_area')
            qualificate_count_dict = {}
            for area_coding, qualificate_list in group_qualificate_data_data:
                if area_coding:
                    if area_coding not in qualificate_count_dict.keys():
                        qualificate_count_dict[area_coding] = 0
                    for qualificate in qualificate_list:
                        qualificate_count_dict[area_coding] += 1
                    area_name = __area_coding_to_name__.get(area_coding, '')
                    if area_name:
                        if res.get(area_name, None):
                            res[area_name]['qualificate'] = qualificate_count_dict[area_coding]

            worker_data = worker_query.values('service_area', 'worker_id')
            group_worker_data_data = group_by(list(worker_data), 'service_area')
            worker_count_dict = {}
            for area_coding, worker_list in group_worker_data_data:
                if area_coding:
                    if area_coding not in worker_count_dict.keys():
                        worker_count_dict[area_coding] = 0
                    for worker in worker_list:
                        worker_count_dict[area_coding] += 1
                    area_name = __area_coding_to_name__.get(area_coding, '')
                    if area_name:
                        if res.get(area_name, None):
                            res[area_name]['worker'] = worker_count_dict[area_coding]

            for k, value in res.items():
                value['name'] = k
                res_list.append(value)
        return Response({'data': res_list, 'code': 200})


class CityCompanyStatistics(APIView):
    """市入围公司详情统计"""

    permission_classes = [MyPermissionCity]

    def get(self, request, **kwargs):
        area_coding = self.request.GET.get('area_coding', None)
        company_name = self.request.GET.get('company_name', None)
        enter_type = self.request.GET.get('enter_type', 1)
        if not area_coding:
            return Response({"code": 400, "msg": "缺少参数!"})
        area_name = __area_coding_to_name__.get(area_coding, '')
        if company_name:
            comp_list = Company.objects.filter(name__icontains=company_name).values_list('company_id', flat=True)
        else:
            comp_list = Company.objects.values_list('company_id', flat=True)
        if comp_list:
            com_query = CompanyArea.objects.filter(company_id__in=comp_list)
        else:
            com_query = CompanyArea.objects.all()
        if int(enter_type) == 1:
            company_data = com_query.filter(area_coding=area_coding, enter_type=1, is_delete=0).values_list(
                'company_id', flat=True)
        else:
            company_data = com_query.filter(area_coding=area_coding, selected_type=1, is_delete=0).values_list(
                'company_id', flat=True)
        if company_data:
            company_id_list = company_data
        else:
            company_id_list = []
        res = {}

        com_query = Company.objects.filter(company_id__in=company_id_list)
        for com in com_query:
            res[com.name] = {'company_id': com.company_id, 'car': 0, 'contract': 0, 'qualificate': 0, 'worker': 0,
                             'area_name': area_name}
        car_data = Car.objects.values('company_id', 'company_name').annotate(count=Count('company_id')). \
            filter(company_id__in=company_id_list, enter_type=1, is_delete=0)
        for car in car_data:
            company_name = car.get('company_name')
            res[company_name]['car'] = car.get('count', 0)

        qualificate_data = Qualificate.objects.values('company_id', 'company_name'). \
            annotate(count=Count('company_id')).filter(company_id__in=company_id_list, enter_type=1, is_delete=0)
        for qualificate in qualificate_data:
            company_name = qualificate.get('company_name')
            res[company_name]['qualificate'] = qualificate.get('count', 0)

        worker_data = Worker.objects.values('company_id', 'company_name'). \
            annotate(count=Count('company_id')).filter(company_id__in=company_id_list, enter_type=1, is_delete=0)
        for worker in worker_data:
            company_name = worker.get('company_name')
            res[company_name]['worker'] = worker.get('count', 0)

        # contract_data = Contract.objects.values('company_id', 'company_name').\
        #     annotate(count=Count('company_id')).filter(company_id__in=company_id_list, enter_type=1, is_delete=0)
        # for contract in contract_data:
        #     company_name = contract.get('company_name')
        #     res[company_name]['contract'] = contract.get('count', 0)

        res_list = []
        for k, value in res.items():
            value['name'] = k
            res_list.append(value)
        return Response({'data': res_list, 'code': 200})


class CheckOtherCar(APIView):
    """区审核车辆通过不通过"""

    def post(self, request, **kwargs):
        car_id = self.request.data.get('car_id', None)
        status = self.request.data.get('status', None)
        no_reject = self.request.data.get('no_reject', None)
        username = self.request.data.get('username', None)
        enter_time = datetime.datetime.now()
        queryset = OtherCar.objects.filter(car_id=car_id)
        car_data = queryset.values('car_num', 'company_id', 'status', 'check_type')
        if car_data and car_data[0]:
            check_type = car_data[0].get('check_type')
        else:
            check_type = 0
        if status == 0:  # 通过
            queryset.update(status=3, enter_time=enter_time, enter_operater=username, check_type=1,
                            enter_ip=get_ip(request))
            # 审核通过后, 从市级添加车辆信息
            car_obj = OtherCar.objects.filter(car_id=car_id).first()
            logger.info('审核通过后, 从市级添加车辆信息')
            c_response = add_car_db(car_obj)
            logger.info('add car ==============:%s' % c_response)
            if c_response == 0:
                return Response({"code": 400, "msg": "非居民已注册该车辆，不能重复注册!"})
            # 车辆添加记录
            if check_type == 0:
                other_car_opera_detail(queryset.values())
            else:
                check_other_car_opera_detail(queryset.values())

            return Response({'msg': "区审核车辆通过成功", 'code': 200})
        else:
            if not no_reject:
                return Response({'msg': "请输入审核不通过原因", 'code': 400})
            OtherCar.objects.filter(car_id=car_id).update(status=2, no_reject=no_reject, enter_time=enter_time,
                                                          enter_operater=username, enter_ip=get_ip(request))
            OtherCarOperate.objects.filter(car_id=car_id, is_delete=2).update(is_delete=1)
            return Response({'msg': "区审核车辆不通过成功", 'code': 200})
