# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <j<PERSON><PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2014
# <AUTHOR> <EMAIL>, 2014
# <AUTHOR> <EMAIL>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-23 18:54+0000\n"
"Last-Translator: Pãnoș <<EMAIL>>\n"
"Language-Team: Greek (http://www.transifex.com/django/django/language/el/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: el\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Sites"
msgstr "Ιστότοποι"

msgid "The domain name cannot contain any spaces or tabs."
msgstr "Το όνομα τομέα δεν μπορεί να περιέχει κενά."

msgid "domain name"
msgstr "όνομα τομέα"

msgid "display name"
msgstr "εμφανιζόμενο όνομα"

msgid "site"
msgstr "ιστότοπος"

msgid "sites"
msgstr "ιστότοποι"
