from rest_framework.permissions import BasePermission
from utils.request import BaseRequests, message_url
from rest_framework import exceptions
from django.utils.translation import gettext_lazy as _


# 权限判定
def _permission(request):
    auth_token = request.META.get('HTTP_AUTHORIZATION')
    if auth_token:
        auth_token = auth_token.split(' ')[-1]
        if auth_token == 'xwanzicoming':
            role = 'CityManager'
            return role
    response = BaseRequests(message_url.format(auth_token)).get()
    request.token = auth_token

    if response.get('code') != 200:
        return False
    else:
        manager_data = response.get('data').get('manager')
        role = manager_data.get('role')
        return role


class MyPermissionQualification(BasePermission):
    """
    Allows access only to authenticated users.
    """

    def has_permission(self, request, view):
        role = _permission(request)
        if role == 'Qualification':
            return True
        else:
            return False


class MyPermission(BasePermission):
    message = '无权访问'
    
    def has_permission(self, request, view):
        role = _permission(request)
        if role == 'CityManager' or role == 'AreaManager' or role == 'StreetManager' or role == 'Platform':
            if hasattr(request, 'user'):
                user = request.user
                if user == 1:
                    return True
                if "manager" in user:
                    check_coding(request, user.get('manager'))
            return True
        else:
            return False


class MyPermissionCity(BasePermission):
    message = '无权访问'
    
    def has_permission(self, request, view):
        role = _permission(request)
        if role == 'CityManager':
            return True
        else:
            return False


class MyPermissionArea(BasePermission):
    message = '无权访问'
    
    
    def has_permission(self, request, view):
        role = _permission(request)
        if role == 'AreaManager':
            return True
        else:
            return False


class MyPermissionStreet(BasePermission):
    message = '无权访问'
    
    
    def has_permission(self, request, view):
        role = _permission(request)
        if role == 'StreetManager':
            return True
        else:
            return False


class MyPermissionCar(BasePermission):
    message = '无权访问'

    def has_permission(self, request, view):
        role = _permission(request)
        if role == 'TransportDriver':
            return True
        else:
            return False


class MyPermissionCleaningPointManager(BasePermission):
    message = '无权访问'

    def has_permission(self, request, view):
        role = _permission(request)
        print(role)
        if role == 'CleaningPointCollectorManager':
            return True
        else:
            return False


class MyPermissionCollectAppManager(BasePermission):
    message = '无权访问'

    def has_permission(self, request, view):
        role = _permission(request)
        print(role)
        if role in ["TransportDriver", 'CleaningPointCollectorManager']:
            return True
        else:
            return False


class CheckCodingPermission(BasePermission):
    """
    检查 coding 权限
    """
    message = '无权访问'

    def has_permission(self, request, view):
        if hasattr(request, 'user'):
            user = request.user
            if user == 1:
                return True
            if "manager" in user:
                check_coding(request, user.get('manager'))
        # 不负责检查用户是否登录，只在用户登录时候检查 coding 权限
        return True


def check_coding(request, manager):
    """区域编码权限验证"""

    # TODO: 增加其它类型角色校验
    get = request.GET.copy()
    query_params = request.GET.copy()
    if manager.get("role") == "AreaManager":
        if 'area_coding' in get and get['area_coding']:
            if get['area_coding'] != manager.get("area_coding"):
                raise exceptions.PermissionDenied(_('您没有权限访问该数据'))
        else:
            get['area_coding'] = manager.get("area_coding")
            query_params['area_coding'] = manager.get("area_coding")
    elif manager.get("role") == "StreetManager":
        if 'area_coding' in get and get['area_coding']:
            if not get['area_coding'].startswith(manager.get("street_coding")[0:6]):
                raise exceptions.PermissionDenied(_('您没有权限访问该数据'))
        else:
            get['area_coding'] = manager.get("street_coding")[0:6] + "000000"
            query_params['area_coding'] = manager.get("street_coding")[0:6] + "000000"
        if 'street_coding' in get and get['street_coding']:
            if get['street_coding'] != manager.get("street_coding"):
                raise exceptions.PermissionDenied(_('您没有权限访问该数据'))
        else:
            get['street_coding'] = manager.get("street_coding")
            query_params['street_coding'] = manager.get("street_coding")
    request._request.GET = query_params
    request.GET = get
